# Question 5: Money Transfer Service Code Review

## Problem Analysis

The provided `TransferService` code contains critical issues that make it unsafe for production use in a high-throughput, multi-server environment processing hundreds of transfers per second.

## Critical Issues Identified

### 1. Race Conditions & Data Consistency

**Issue:** Lost-update race conditions and partial transfers
- Two concurrent transfers can read the same balance simultaneously, leading to overdrafts
- Source account is debited before destination is credited - system crash loses money
- No atomic operations ensure balance consistency

**Fix:** Wrap entire transfer in database transaction for atomicity

### 2. Duplicate Processing

**Issue:** No idempotency protection
- Client retries can execute the same transfer multiple times
- No unique transfer identification mechanism

**Fix:** Add transfer ID parameter and check for duplicates

### 3. Monetary Precision

**Issue:** Decimal precision drift
- Uses `Decimal` but never quantizes amounts
- Arithmetic can introduce precision errors

**Fix:** Quantize all amounts to 2 decimal places using `ROUND_HALF_UP`

### 4. Timezone Issues

**Issue:** Naive timestamps
- `datetime.now()` creates timezone-naive objects
- Inconsistent across multi-server deployments

**Fix:** Use `datetime.now(tz=timezone.utc)` for consistent timestamps

### 5. Poor Error Handling

**Issue:** Generic exception handling
- Catches all exceptions and returns boolean
- Callers can't distinguish between different failure types

**Fix:** Use specific exception types for different error scenarios

### 6. Design Issues

**Issue:** Mixed responsibilities
- Service creates its own logger
- Violates Single Responsibility Principle

**Fix:** Inject logger dependency for better testability

## Solution Implementation

```python
import logging
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from uuid import UUID, uuid4
from typing import Optional

# Monetary precision constant
CENT = Decimal("0.01")

# Domain-specific exceptions
class TransferError(Exception):
    """Base exception for transfer operations."""
    pass

class AccountNotFound(TransferError):
    """Raised when an account cannot be found."""
    pass

class InsufficientFunds(TransferError):
    """Raised when source account has insufficient balance."""
    pass

class DuplicateTransfer(TransferError):
    """Raised when attempting to process a duplicate transfer."""
    pass

class TransferService:
    """
    Money transfer service with proper error handling and transaction safety.

    Fixes the critical issues in the original implementation:
    - Uses database transactions for atomicity
    - Prevents duplicate transfers with transfer IDs
    - Proper decimal precision handling
    - UTC timestamps for consistency
    - Specific exception types for clear error handling
    """

    def __init__(self, account_repository, logger: logging.Logger):
        self.account_repository = account_repository
        self.logger = logger

    def transfer_money(
        self,
        from_account_id: str,
        to_account_id: str,
        raw_amount: Decimal,
        transfer_id: Optional[UUID] = None
    ) -> UUID:
        """
        Transfer money between accounts with proper error handling and atomicity.

        Args:
            from_account_id: Source account ID
            to_account_id: Destination account ID
            raw_amount: Amount to transfer (will be quantized to cents)
            transfer_id: Optional idempotency key to prevent duplicates

        Returns:
            UUID: Transfer identifier for tracking

        Raises:
            AccountNotFound: When source or destination account doesn't exist
            InsufficientFunds: When source account has insufficient balance
            DuplicateTransfer: When transfer_id already processed
        """
        # Generate transfer ID if not provided
        transfer_id = transfer_id or uuid4()

        # Quantize amount to prevent precision issues
        amount = raw_amount.quantize(CENT, ROUND_HALF_UP)

        try:
            # Start database transaction for atomicity
            with self.account_repository.transaction():

                # Check for duplicate transfer
                if self.account_repository.transfer_exists(transfer_id):
                    raise DuplicateTransfer(f"Transfer {transfer_id} already processed")

                # Find and validate accounts exist
                source_account = self.account_repository.find_by_id(from_account_id)
                if not source_account:
                    raise AccountNotFound(f"Source account not found: {from_account_id}")

                destination_account = self.account_repository.find_by_id(to_account_id)
                if not destination_account:
                    raise AccountNotFound(f"Destination account not found: {to_account_id}")

                # Validate sufficient funds
                if source_account.balance < amount:
                    raise InsufficientFunds(
                        f"Insufficient funds: balance={source_account.balance}, required={amount}"
                    )

                # Execute transfer with UTC timestamp
                timestamp = datetime.now(tz=timezone.utc)

                # Update balances
                source_account.balance -= amount
                source_account.last_updated = timestamp
                destination_account.balance += amount
                destination_account.last_updated = timestamp

                # Save changes atomically
                self.account_repository.save(source_account)
                self.account_repository.save(destination_account)

                # Record transfer for audit and idempotency
                self.account_repository.record_transfer(
                    transfer_id, from_account_id, to_account_id, amount, timestamp
                )

            # Log successful transfer
            self.logger.info(
                f"Transfer completed: {transfer_id} - {amount} from {from_account_id} to {to_account_id}"
            )
            return transfer_id

        except TransferError:
            # Re-raise business errors as-is
            raise
        except Exception as e:
            # Log and wrap system errors
            self.logger.error(f"Transfer failed due to system error: {e}", exc_info=True)
            raise TransferError("Transfer failed due to system error") from e
```

## Key Improvements

### 1. **Atomicity**: Database transactions ensure complete success or complete rollback
### 2. **Idempotency**: Transfer IDs prevent duplicate processing
### 3. **Precision**: All amounts quantized to 2 decimal places
### 4. **Consistency**: UTC timestamps across all operations
### 5. **Error Handling**: Specific exceptions for different failure scenarios
### 6. **Clean Design**: Injected logger dependency for better testability

This solution addresses all critical issues while maintaining simplicity appropriate for a home test assessment.
