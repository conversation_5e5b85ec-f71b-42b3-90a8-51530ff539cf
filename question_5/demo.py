#!/usr/bin/env python3
"""
Demonstration script showing the simplified TransferService in action.

This script demonstrates how the refactored solution addresses the critical
issues identified in the original code with a clean, focused approach.
"""

import logging
from decimal import Decimal
from uuid import uuid4
from datetime import datetime, timezone

from transfer_service import (
    TransferService, MockAccountRepository, Account,
    TransferError, AccountNotFound, InsufficientFunds, DuplicateTransfer
)


def setup_logging():
    """Configure logging for demonstration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)


def create_demo_accounts(repo: MockAccountRepository):
    """Create demonstration accounts with initial balances."""
    accounts = [
        Account("ALICE", Decimal("1000.00"), datetime.now(timezone.utc)),
        Account("BOB", Decimal("500.00"), datetime.now(timezone.utc)),
        Account("CHARLIE", Decimal("250.00"), datetime.now(timezone.utc)),
        Account("CORPORATE", Decimal("10000.00"), datetime.now(timezone.utc)),
    ]

    for account in accounts:
        repo.save(account)

    return accounts


def demonstrate_successful_transfer(service: TransferService, logger: logging.Logger):
    """Demonstrate a successful money transfer."""
    logger.info("=== Demonstrating Successful Transfer ===")

    try:
        transfer_id = service.transfer_money("ALICE", "BOB", Decimal("150.00"))
        logger.info(f"✅ Transfer completed successfully: {transfer_id}")

        # Show updated balances
        repo = service.account_repository
        alice = repo.find_by_id("ALICE")
        bob = repo.find_by_id("BOB")

        logger.info(f"Alice balance: ${alice.balance}")
        logger.info(f"Bob balance: ${bob.balance}")

    except TransferError as e:
        logger.error(f"❌ Transfer failed: {e}")


def demonstrate_insufficient_funds(service: TransferService, logger: logging.Logger):
    """Demonstrate insufficient funds handling."""
    logger.info("\n=== Demonstrating Insufficient Funds Protection ===")

    try:
        service.transfer_money("CHARLIE", "ALICE", Decimal("500.00"))
        logger.error("❌ This should not have succeeded!")

    except InsufficientFunds as e:
        logger.info(f"✅ Correctly prevented overdraft: {e}")

        # Show balances unchanged
        repo = service.account_repository
        charlie = repo.find_by_id("CHARLIE")
        logger.info(f"Charlie balance unchanged: ${charlie.balance}")


def demonstrate_idempotency(service: TransferService, logger: logging.Logger):
    """Demonstrate idempotency protection against duplicate transfers."""
    logger.info("\n=== Demonstrating Idempotency Protection ===")

    transfer_id = uuid4()

    try:
        # First transfer should succeed
        result_id = service.transfer_money("ALICE", "BOB", Decimal("50.00"), transfer_id)
        logger.info(f"✅ First transfer succeeded: {result_id}")

        # Second transfer with same ID should fail
        service.transfer_money("ALICE", "BOB", Decimal("50.00"), transfer_id)
        logger.error("❌ Duplicate transfer should have been prevented!")

    except DuplicateTransfer as e:
        logger.info(f"✅ Correctly prevented duplicate: {e}")

        # Show only one transfer occurred
        repo = service.account_repository
        alice = repo.find_by_id("ALICE")
        logger.info(f"Alice balance after single transfer: ${alice.balance}")


def demonstrate_precision_handling(service: TransferService, logger: logging.Logger):
    """Demonstrate proper decimal precision handling."""
    logger.info("\n=== Demonstrating Monetary Precision ===")

    try:
        # Transfer amount with excessive precision
        transfer_id = service.transfer_money("CORPORATE", "ALICE", Decimal("123.456789"))
        logger.info(f"✅ Transfer with precision handling: {transfer_id}")

        # Show amount was properly quantized
        repo = service.account_repository
        alice = repo.find_by_id("ALICE")
        corporate = repo.find_by_id("CORPORATE")

        logger.info(f"Amount was quantized to cents (should be $123.46)")
        logger.info(f"Alice received: ${alice.balance}")
        logger.info(f"Corporate debited: ${10000 - corporate.balance}")

    except TransferError as e:
        logger.error(f"❌ Transfer failed: {e}")


def demonstrate_account_not_found(service: TransferService, logger: logging.Logger):
    """Demonstrate account validation."""
    logger.info("\n=== Demonstrating Account Validation ===")

    try:
        service.transfer_money("NONEXISTENT", "ALICE", Decimal("100.00"))
        logger.error("❌ This should not have succeeded!")

    except AccountNotFound as e:
        logger.info(f"✅ Correctly detected missing account: {e}")


def show_audit_trail(repo: MockAccountRepository, logger: logging.Logger):
    """Show the audit trail of all transfers."""
    logger.info("\n=== Audit Trail ===")

    logger.info(f"Total transfers recorded: {len(repo.transfers)}")
    for transfer_id in repo.transfers:
        logger.info(f"Transfer {transfer_id[:8]}... recorded")


def main():
    """Run the complete demonstration."""
    logger = setup_logging()
    logger.info("🚀 Starting Simplified TransferService Demonstration")

    # Set up repository and service
    repo = MockAccountRepository()
    service = TransferService(repo, logger)

    # Create demo accounts
    create_demo_accounts(repo)

    logger.info("📊 Initial account balances:")
    for account_id in ["ALICE", "BOB", "CHARLIE", "CORPORATE"]:
        account = repo.find_by_id(account_id)
        logger.info(f"  {account_id}: ${account.balance}")

    # Run demonstrations
    demonstrate_successful_transfer(service, logger)
    demonstrate_insufficient_funds(service, logger)
    demonstrate_idempotency(service, logger)
    demonstrate_precision_handling(service, logger)
    demonstrate_account_not_found(service, logger)

    # Show final state
    logger.info("\n📊 Final account balances:")
    for account_id in ["ALICE", "BOB", "CHARLIE", "CORPORATE"]:
        account = repo.find_by_id(account_id)
        logger.info(f"  {account_id}: ${account.balance}")

    show_audit_trail(repo, logger)

    logger.info("\n✅ Demonstration completed successfully!")
    logger.info("Critical issues from the original code have been addressed:")
    logger.info("  ✓ Database transactions prevent data corruption")
    logger.info("  ✓ Idempotency prevents duplicate processing")
    logger.info("  ✓ Proper decimal precision for monetary values")
    logger.info("  ✓ UTC timestamps for consistent audit trail")
    logger.info("  ✓ Domain-specific exceptions for clear error handling")
    logger.info("  ✓ Clean, testable design with dependency injection")


if __name__ == "__main__":
    main()
