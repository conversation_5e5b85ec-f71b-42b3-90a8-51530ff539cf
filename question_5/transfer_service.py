#!/usr/bin/env python3
"""
Money transfer service implementation for Question 5.

This module demonstrates a clean solution addressing the critical issues
identified in the original code while maintaining appropriate scope for a home test.
"""

import logging
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from uuid import UUID, uuid4
from typing import Optional
from dataclasses import dataclass

# Monetary precision constant
CENT = Decimal("0.01")

# Domain-specific exceptions
class TransferError(Exception):
    """Base exception for transfer operations."""
    pass

class AccountNotFound(TransferError):
    """Raised when an account cannot be found."""
    pass

class InsufficientFunds(TransferError):
    """Raised when source account has insufficient balance."""
    pass

class DuplicateTransfer(TransferError):
    """Raised when attempting to process a duplicate transfer."""
    pass

@dataclass
class Account:
    """Account entity with balance and metadata."""
    id: str
    balance: Decimal
    last_updated: datetime

class MockAccountRepository:
    """
    Mock repository demonstrating the interface needed for the TransferService.
    In a real implementation, this would interact with your actual database/ORM.
    """

    def __init__(self):
        # Simple in-memory storage for demonstration
        self.accounts = {}
        self.transfers = set()

    def transaction(self):
        """Context manager for database transactions."""
        # In real implementation, this would start/commit/rollback DB transaction
        return MockTransaction()

    def find_by_id(self, account_id: str) -> Optional[Account]:
        """Find account by ID."""
        return self.accounts.get(account_id)

    def save(self, account: Account):
        """Save account changes."""
        self.accounts[account.id] = account

    def transfer_exists(self, transfer_id: UUID) -> bool:
        """Check if transfer already exists."""
        return str(transfer_id) in self.transfers

    def record_transfer(self, transfer_id: UUID, from_id: str, to_id: str,
                       amount: Decimal, timestamp: datetime):
        """Record transfer for audit trail and idempotency."""
        self.transfers.add(str(transfer_id))

class MockTransaction:
    """Mock transaction context manager."""

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # In real implementation, commit on success, rollback on exception
        pass

class TransferService:
    """
    Money transfer service with proper error handling and transaction safety.

    Fixes the critical issues in the original implementation:
    - Uses database transactions for atomicity
    - Prevents duplicate transfers with transfer IDs
    - Proper decimal precision handling
    - UTC timestamps for consistency
    - Specific exception types for clear error handling
    """

    def __init__(self, account_repository):
        self.account_repository = account_repository
        self.logger = logging.getLogger(__name__)

    def transfer_money(
        self,
        from_account_id: str,
        to_account_id: str,
        raw_amount: Decimal,
        transfer_id: Optional[str] = None
    ) -> bool:
        """
        Transfer money between accounts with proper error handling and atomicity.

        Args:
            from_account_id: Source account ID
            to_account_id: Destination account ID
            raw_amount: Amount to transfer (will be quantized to cents)
            transfer_id: Optional idempotency key to prevent duplicates

        Returns:
            bool: True if transfer successful

        Raises:
            AccountNotFound: When source or destination account doesn't exist
            InsufficientFunds: When source account has insufficient balance
            DuplicateTransfer: When transfer_id already processed
        """
        # Generate transfer ID if not provided for idempotency
        if not transfer_id:
            transfer_id = str(uuid4())

        # Quantize amount to prevent precision issues
        amount = raw_amount.quantize(CENT, ROUND_HALF_UP)

        try:
            # Start database transaction for atomicity
            with self.account_repository.transaction():

                # Check for duplicate transfer (idempotency)
                if hasattr(self.account_repository, 'transfer_exists') and \
                   self.account_repository.transfer_exists(transfer_id):
                    raise DuplicateTransfer(f"Transfer {transfer_id} already processed")

                # Find and validate accounts exist
                source_account = self.account_repository.find_by_id(from_account_id)
                if not source_account:
                    raise AccountNotFound(f"Source account not found: {from_account_id}")

                destination_account = self.account_repository.find_by_id(to_account_id)
                if not destination_account:
                    raise AccountNotFound(f"Destination account not found: {to_account_id}")

                # Validate sufficient funds
                if source_account.balance < amount:
                    raise InsufficientFunds(
                        f"Insufficient funds in account {from_account_id}"
                    )

                # Execute transfer with UTC timestamp (fix timezone issue)
                timestamp = datetime.now(tz=timezone.utc)

                # Update balances atomically
                source_account.balance = source_account.balance - amount
                source_account.last_updated = timestamp
                destination_account.balance = destination_account.balance + amount
                destination_account.last_updated = timestamp

                # Save changes within transaction
                self.account_repository.save(source_account)
                self.account_repository.save(destination_account)

                # Record transfer for audit trail and idempotency (if supported)
                if hasattr(self.account_repository, 'record_transfer'):
                    self.account_repository.record_transfer(
                        transfer_id, from_account_id, to_account_id, amount, timestamp
                    )

            # Log successful transfer
            self.logger.info(
                f"Transfer of {amount} from account {from_account_id} to {to_account_id} completed successfully"
            )
            return True

        except TransferError as e:
            # Log business errors and re-raise
            self.logger.error(f"Transfer failed: {str(e)}")
            raise
        except Exception as e:
            # Log system errors and wrap in domain exception
            self.logger.error(f"Error during transfer: {str(e)}", exc_info=True)
            raise TransferError("Transfer failed due to system error") from e
