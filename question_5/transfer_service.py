#!/usr/bin/env python3
"""
Production-ready money transfer service implementation.

This module demonstrates the solution to Question 5, addressing all critical issues
identified in the original code for a high-throughput, multi-server environment.
"""

from __future__ import annotations
import logging
import sqlite3
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from uuid import UUID, uuid4
from contextlib import AbstractContextManager
from typing import Optional, Dict, Any
from dataclasses import dataclass

# Monetary precision constant
CENT = Decimal("0.01")

# Domain-specific exceptions
class TransferError(RuntimeError):
    """Base exception for transfer operations."""
    pass

class AccountNotFound(TransferError):
    """Raised when an account cannot be found."""
    pass

class InsufficientFunds(TransferError):
    """Raised when source account has insufficient balance."""
    pass

class DuplicateTransfer(TransferError):
    """Raised when attempting to process a duplicate transfer."""
    pass

@dataclass
class Account:
    """Account entity with balance and metadata."""
    id: str
    balance: Decimal
    last_updated: datetime
    version: int = 0  # For optimistic locking

class TransactionManager(AbstractContextManager):
    """Manages database transactions with ACID properties."""

    def __init__(self, connection: sqlite3.Connection, serializable: bool = True,
                 idempotency_key: Optional[UUID] = None):
        self.connection = connection
        self.serializable = serializable
        self.idempotency_key = idempotency_key
        self.transaction_started = False

    def __enter__(self):
        # Check if we're already in a transaction
        try:
            # Try to start a transaction
            if self.serializable:
                self.connection.execute("BEGIN IMMEDIATE")
            else:
                self.connection.execute("BEGIN")
            self.transaction_started = True
        except sqlite3.OperationalError as e:
            if "cannot start a transaction within a transaction" in str(e):
                # We're already in a transaction, that's fine for our use case
                self.transaction_started = False
            else:
                raise

        # Check for duplicate transfer if idempotency key provided
        if self.idempotency_key:
            cursor = self.connection.execute(
                "SELECT transfer_id FROM transfers WHERE transfer_id = ?",
                (str(self.idempotency_key),)
            )
            if cursor.fetchone():
                raise DuplicateTransfer(f"Transfer {self.idempotency_key} already processed")

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.transaction_started:
            if exc_type is None:
                self.connection.commit()
            else:
                self.connection.rollback()

class AccountRepository:
    """Repository for account data access with locking support."""
    
    def __init__(self, connection: sqlite3.Connection):
        self.connection = connection
        self._ensure_tables()
    
    def _ensure_tables(self):
        """Create tables if they don't exist."""
        self.connection.executescript("""
            CREATE TABLE IF NOT EXISTS accounts (
                id TEXT PRIMARY KEY,
                balance TEXT NOT NULL,
                last_updated TEXT NOT NULL,
                version INTEGER DEFAULT 0
            );
            
            CREATE TABLE IF NOT EXISTS transfers (
                transfer_id TEXT PRIMARY KEY,
                from_account_id TEXT NOT NULL,
                to_account_id TEXT NOT NULL,
                amount TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                FOREIGN KEY (from_account_id) REFERENCES accounts (id),
                FOREIGN KEY (to_account_id) REFERENCES accounts (id)
            );
            
            CREATE INDEX IF NOT EXISTS idx_transfers_timestamp ON transfers (timestamp);
        """)
        self.connection.commit()
    
    def find_for_update(self, account_id: str) -> Optional[Account]:
        """Find account with pessimistic locking (SELECT ... FOR UPDATE)."""
        # SQLite doesn't support SELECT FOR UPDATE, but IMMEDIATE transaction provides similar protection
        cursor = self.connection.execute(
            "SELECT id, balance, last_updated, version FROM accounts WHERE id = ?",
            (account_id,)
        )
        row = cursor.fetchone()
        if row:
            return Account(
                id=row[0],
                balance=Decimal(row[1]),
                last_updated=datetime.fromisoformat(row[2]),
                version=row[3]
            )
        return None
    
    def save(self, account: Account):
        """Save account changes within current transaction."""
        # Update with optimistic locking check
        cursor = self.connection.execute("""
            UPDATE accounts
            SET balance = ?, last_updated = ?, version = version + 1
            WHERE id = ? AND version = ?
        """, (
            str(account.balance),
            account.last_updated.isoformat(),
            account.id,
            account.version
        ))

        if cursor.rowcount == 0:
            # Account doesn't exist, try insert
            try:
                self.connection.execute("""
                    INSERT INTO accounts (id, balance, last_updated, version)
                    VALUES (?, ?, ?, 0)
                """, (
                    account.id,
                    str(account.balance),
                    account.last_updated.isoformat()
                ))
            except sqlite3.IntegrityError:
                # Account exists but version mismatch - this is expected in concurrent scenarios
                # Re-read the account to get current version and retry
                current = self.find_for_update(account.id)
                if current and current.version != account.version:
                    # Update the account version and retry
                    account.version = current.version
                    cursor = self.connection.execute("""
                        UPDATE accounts
                        SET balance = ?, last_updated = ?, version = version + 1
                        WHERE id = ? AND version = ?
                    """, (
                        str(account.balance),
                        account.last_updated.isoformat(),
                        account.id,
                        account.version
                    ))
                    if cursor.rowcount == 0:
                        raise TransferError(f"Concurrent modification detected for account {account.id}")
                else:
                    raise TransferError(f"Failed to save account {account.id}")
    
    def record_transfer(self, transfer_id: UUID, from_id: str, to_id: str, 
                       amount: Decimal, timestamp: datetime):
        """Record transfer for audit trail and idempotency."""
        self.connection.execute("""
            INSERT INTO transfers (transfer_id, from_account_id, to_account_id, amount, timestamp)
            VALUES (?, ?, ?, ?, ?)
        """, (
            str(transfer_id),
            from_id,
            to_id,
            str(amount),
            timestamp.isoformat()
        ))

class TransferService:
    """
    Production-ready money transfer service with ACID guarantees.
    
    Features:
    - Atomic transfers with database transactions
    - Deadlock prevention through deterministic locking
    - Idempotency support with transfer IDs
    - Structured logging for audit trails
    - Domain-specific exceptions for error handling
    """
    
    def __init__(
        self,
        repo: AccountRepository,
        connection: sqlite3.Connection,
        logger: logging.Logger
    ):
        self.repo = repo
        self.connection = connection
        self.log = logger

    def transfer(
        self,
        from_id: str,
        to_id: str,
        raw_amount: Decimal,
        transfer_id: Optional[UUID] = None
    ) -> UUID:
        """
        Execute a money transfer between accounts.
        
        Args:
            from_id: Source account identifier
            to_id: Destination account identifier
            raw_amount: Transfer amount (will be quantized to cents)
            transfer_id: Optional idempotency key
            
        Returns:
            UUID: Transfer identifier for tracking
            
        Raises:
            AccountNotFound: When source or destination account doesn't exist
            InsufficientFunds: When source account has insufficient balance
            DuplicateTransfer: When transfer_id already processed
            TransferError: For unexpected system errors
        """
        transfer_id = transfer_id or uuid4()
        amount: Decimal = raw_amount.quantize(CENT, ROUND_HALF_UP)

        start = datetime.now(tz=timezone.utc)
        
        try:
            with TransactionManager(self.connection, serializable=True, 
                                  idempotency_key=transfer_id):
                # Lock accounts in deterministic order to prevent deadlocks
                account_ids = sorted({from_id, to_id})
                accounts = {}
                for aid in account_ids:
                    account = self.repo.find_for_update(aid)
                    if account:
                        accounts[aid] = account
                
                source_account = accounts.get(from_id)
                destination_account = accounts.get(to_id)

                # Validate accounts exist
                if not source_account:
                    raise AccountNotFound(f"Source account not found: {from_id}")
                if not destination_account:
                    raise AccountNotFound(f"Destination account not found: {to_id}")

                # Validate sufficient funds
                if source_account.balance < amount:
                    raise InsufficientFunds(
                        f"Insufficient funds: balance={source_account.balance}, "
                        f"required={amount}"
                    )

                # Execute atomic balance updates
                timestamp = datetime.now(tz=timezone.utc)
                source_account.balance -= amount
                source_account.last_updated = timestamp
                destination_account.balance += amount
                destination_account.last_updated = timestamp

                # Persist changes within transaction
                self.repo.save(source_account)
                self.repo.save(destination_account)
                
                # Record transfer for audit trail
                self.repo.record_transfer(transfer_id, from_id, to_id, amount, timestamp)

            # Log successful transfer with structured data
            self.log.info(
                "transfer.completed",
                extra={
                    "transfer_id": str(transfer_id),
                    "from_account_id": from_id,
                    "to_account_id": to_id,
                    "amount": str(amount),
                    "duration_ms": (
                        datetime.now(tz=timezone.utc) - start
                    ).total_seconds() * 1000,
                    "timestamp": timestamp.isoformat()
                }
            )
            return transfer_id

        except TransferError:
            # Log business errors and re-raise
            self.log.warning(
                "transfer.business_error",
                exc_info=True,
                extra={
                    "transfer_id": str(transfer_id),
                    "from_account_id": from_id,
                    "to_account_id": to_id,
                    "amount": str(amount)
                }
            )
            raise

        except Exception as e:
            # Log system errors and wrap in domain exception
            self.log.exception(
                "transfer.system_error",
                extra={
                    "transfer_id": str(transfer_id),
                    "from_account_id": from_id,
                    "to_account_id": to_id,
                    "amount": str(amount),
                    "error": str(e)
                }
            )
            raise TransferError("Transfer failed due to system error") from e
