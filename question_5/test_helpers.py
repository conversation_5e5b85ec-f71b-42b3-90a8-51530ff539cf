#!/usr/bin/env python3
"""
Test helpers for the TransferService implementation.

This module provides simple implementations of the repository interface
for testing purposes only.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from transfer_service import Account


class TestAccountRepository:
    """Simple in-memory repository for testing."""
    
    def __init__(self):
        self.accounts = {}
        self.transfers = set()
    
    def transaction(self):
        return TestTransaction()
    
    def find_by_id(self, account_id: str) -> Optional[Account]:
        return self.accounts.get(account_id)
    
    def save(self, account: Account):
        self.accounts[account.id] = account
    
    def transfer_exists(self, transfer_id: str) -> bool:
        return transfer_id in self.transfers
    
    def record_transfer(self, transfer_id: str, from_id: str, to_id: str, 
                       amount: Decimal, timestamp: datetime):
        self.transfers.add(transfer_id)


class TestTransaction:
    """Simple transaction context manager for testing."""
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
