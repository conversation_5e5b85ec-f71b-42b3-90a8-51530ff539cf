"""
Comprehensive security test suite for the Secure Transfer API.

Tests cover:
- IDOR/BOLA attack prevention
- SQL injection prevention
- Input validation and sanitization
- Authorization checks
- Rate limiting
- Error handling security
- Security headers validation
"""

import pytest
import json
from unittest.mock import Mock, patch
from decimal import Decimal
from datetime import datetime, timezone

from secure_solution import (
    app,
    TransferRequest,
    SecureAccountRepository,
    AuthorizationService,
    SecureTransferService,
    ValidationError,
    AuthorizationError,
    AccountNotFoundError,
    SecurityError
)

@pytest.fixture
def client():
    """Create test client."""
    app.config['TESTING'] = True
    app.config['JWT_SECRET_KEY'] = 'test-secret-key'
    with app.test_client() as client:
        yield client

@pytest.fixture
def mock_jwt_token():
    """Mock JWT token for testing."""
    with patch('secure_solution.get_jwt_identity') as mock_jwt:
        mock_jwt.return_value = 'test_user_123'
        yield mock_jwt

@pytest.fixture
def mock_db_session():
    """Mock database session."""
    return Mock()

class TestTransferRequestValidation:
    """Test input validation and sanitization."""
    
    def test_valid_transfer_request(self):
        """Test valid transfer request creation."""
        data = {
            'source_account_id': 'acc_123',
            'destination_account_id': 'acc_456',
            'amount': '100.50',
            'description': 'Test transfer'
        }
        
        request = TransferRequest.from_dict(data)
        assert request.source_account_id == 'acc_123'
        assert request.destination_account_id == 'acc_456'
        assert request.amount == Decimal('100.50')
        assert request.description == 'Test transfer'
    
    def test_missing_required_fields(self):
        """Test validation of missing required fields."""
        data = {'source_account_id': 'acc_123'}
        
        with pytest.raises(ValidationError, match="Missing required field"):
            TransferRequest.from_dict(data)
    
    def test_invalid_account_id_format(self):
        """Test validation of account ID format."""
        data = {
            'source_account_id': 'acc_123<script>',  # XSS attempt
            'destination_account_id': 'acc_456',
            'amount': '100.00'
        }
        
        with pytest.raises(ValidationError, match="Invalid source_account_id format"):
            TransferRequest.from_dict(data)
    
    def test_negative_amount(self):
        """Test validation of negative amounts."""
        data = {
            'source_account_id': 'acc_123',
            'destination_account_id': 'acc_456',
            'amount': '-100.00'
        }
        
        with pytest.raises(ValidationError, match="Transfer amount must be positive"):
            TransferRequest.from_dict(data)
    
    def test_excessive_amount(self):
        """Test validation of excessive amounts."""
        data = {
            'source_account_id': 'acc_123',
            'destination_account_id': 'acc_456',
            'amount': '2000000.00'  # Exceeds limit
        }
        
        with pytest.raises(ValidationError, match="exceeds maximum limit"):
            TransferRequest.from_dict(data)
    
    def test_self_transfer_prevention(self):
        """Test prevention of self-transfers."""
        data = {
            'source_account_id': 'acc_123',
            'destination_account_id': 'acc_123',  # Same account
            'amount': '100.00'
        }
        
        with pytest.raises(ValidationError, match="Cannot transfer to the same account"):
            TransferRequest.from_dict(data)
    
    def test_description_sanitization(self):
        """Test description sanitization."""
        data = {
            'source_account_id': 'acc_123',
            'destination_account_id': 'acc_456',
            'amount': '100.00',
            'description': 'Test <script>alert("xss")</script> transfer'
        }
        
        request = TransferRequest.from_dict(data)
        assert '<script>' not in request.description
        assert 'alert' in request.description  # Content preserved, tags removed

class TestAuthorizationSecurity:
    """Test authorization and access control."""
    
    def test_account_ownership_verification(self, mock_db_session):
        """Test account ownership verification."""
        # Mock account data
        mock_account = {
            'id': 'acc_123',
            'profile_id': 'user_123',
            'balance': Decimal('1000.00'),
            'status': 'ACTIVE'
        }
        
        repo = SecureAccountRepository(mock_db_session)
        repo.find_account_by_id = Mock(return_value=mock_account)
        
        # Test valid ownership
        assert repo.verify_account_ownership('user_123', 'acc_123') == True
        
        # Test invalid ownership
        assert repo.verify_account_ownership('user_456', 'acc_123') == False
    
    def test_transfer_authorization_valid(self, mock_db_session):
        """Test valid transfer authorization."""
        mock_source_account = {
            'id': 'acc_123',
            'profile_id': 'user_123',
            'balance': Decimal('1000.00')
        }
        mock_dest_account = {
            'id': 'acc_456',
            'profile_id': 'user_456',
            'balance': Decimal('500.00')
        }
        
        repo = SecureAccountRepository(mock_db_session)
        repo.find_account_by_id = Mock(side_effect=[mock_source_account, mock_dest_account])
        repo.verify_account_ownership = Mock(return_value=True)
        
        auth_service = AuthorizationService(repo)
        
        request = TransferRequest(
            source_account_id='acc_123',
            destination_account_id='acc_456',
            amount=Decimal('100.00')
        )
        
        # Should not raise exception
        auth_service.validate_transfer_authorization('user_123', request)
    
    def test_transfer_authorization_invalid_ownership(self, mock_db_session):
        """Test transfer authorization with invalid ownership."""
        repo = SecureAccountRepository(mock_db_session)
        repo.verify_account_ownership = Mock(return_value=False)
        
        auth_service = AuthorizationService(repo)
        
        request = TransferRequest(
            source_account_id='acc_123',
            destination_account_id='acc_456',
            amount=Decimal('100.00')
        )
        
        with pytest.raises(AuthorizationError, match="not authorized"):
            auth_service.validate_transfer_authorization('user_456', request)

class TestSQLInjectionPrevention:
    """Test SQL injection prevention."""
    
    def test_parameterized_query_usage(self, mock_db_session):
        """Test that parameterized queries are used."""
        repo = SecureAccountRepository(mock_db_session)
        
        # Attempt SQL injection
        malicious_account_id = "1'; DROP TABLE accounts; --"
        
        repo.find_account_by_id(malicious_account_id)
        
        # Verify parameterized query was called
        mock_db_session.execute.assert_called_once()
        call_args = mock_db_session.execute.call_args
        
        # Check that parameters are passed separately
        assert len(call_args[0]) == 2  # query and parameters
        assert malicious_account_id in call_args[0][1].values()

class TestAPIEndpointSecurity:
    """Test API endpoint security measures."""
    
    def test_authentication_required(self, client):
        """Test that authentication is required."""
        response = client.post('/api/transfers/execute', 
                             json={'source_account_id': 'acc_123'})
        
        assert response.status_code == 401  # Unauthorized
    
    def test_rate_limiting(self, client, mock_jwt_token):
        """Test rate limiting functionality."""
        # This would require actual rate limiting setup
        # For now, just test that the decorator is applied
        transfer_data = {
            'source_account_id': 'acc_123',
            'destination_account_id': 'acc_456',
            'amount': '100.00'
        }
        
        # Multiple rapid requests should eventually be rate limited
        # (This test would need actual rate limiting configuration)
        response = client.post('/api/transfers/execute',
                             json=transfer_data,
                             headers={'Authorization': 'Bearer test_token'})
        
        # Response should indicate rate limiting is in place
        assert response.status_code in [200, 400, 401, 429]  # Various expected responses
    
    def test_security_headers(self, client):
        """Test that security headers are present."""
        response = client.get('/health')
        
        # Check for security headers
        assert 'X-Content-Type-Options' in response.headers
        assert response.headers['X-Content-Type-Options'] == 'nosniff'
        assert 'X-Frame-Options' in response.headers
        assert response.headers['X-Frame-Options'] == 'DENY'
        assert 'X-XSS-Protection' in response.headers
    
    def test_error_handling_no_information_disclosure(self, client, mock_jwt_token):
        """Test that errors don't disclose sensitive information."""
        # Send malformed request
        response = client.post('/api/transfers/execute',
                             json={'invalid': 'data'},
                             headers={'Authorization': 'Bearer test_token'})
        
        response_data = json.loads(response.data)
        
        # Error message should be generic, not expose internals
        assert 'success' in response_data
        assert response_data['success'] == False
        assert 'error' in response_data
        
        # Should not contain stack traces or internal details
        error_text = str(response_data).lower()
        assert 'traceback' not in error_text
        assert 'exception' not in error_text
        assert 'database' not in error_text

class TestBusinessLogicSecurity:
    """Test business logic security."""
    
    def test_idor_attack_prevention(self, mock_db_session):
        """Test IDOR (Insecure Direct Object Reference) attack prevention."""
        # Simulate attacker trying to access victim's account
        attacker_profile = 'attacker_123'
        victim_account = 'victim_account_456'
        
        repo = SecureAccountRepository(mock_db_session)
        repo.verify_account_ownership = Mock(return_value=False)
        
        auth_service = AuthorizationService(repo)
        
        request = TransferRequest(
            source_account_id=victim_account,
            destination_account_id='attacker_account_789',
            amount=Decimal('1000.00')
        )
        
        # Should prevent unauthorized access
        with pytest.raises(AuthorizationError):
            auth_service.validate_transfer_authorization(attacker_profile, request)
    
    def test_concurrent_transfer_safety(self, mock_db_session):
        """Test that concurrent transfers are handled safely."""
        # This would test transaction isolation and locking
        # For now, verify that the service structure supports it
        
        repo = SecureAccountRepository(mock_db_session)
        auth_service = AuthorizationService(repo)
        transfer_service = SecureTransferService(repo, auth_service)
        
        # Verify service can handle concurrent scenarios
        assert hasattr(transfer_service, 'execute_transfer')
        assert hasattr(repo, 'find_account_by_id')

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
