# Question 6: Secure Transfer API Dependencies

# Core Flask framework
Flask>=2.3.0

# JWT authentication
Flask-JWT-Extended>=4.5.0

# Rate limiting
Flask-Limiter>=3.5.0

# Database ORM
SQLAlchemy>=2.0.0

# Testing framework
pytest>=7.0.0
pytest-flask>=1.2.0

# Security testing
pytest-security>=0.1.0

# Code quality
black>=22.0.0
flake8>=5.0.0
mypy>=1.0.0

# Production WSGI server (optional)
# gunicorn>=21.0.0

# Database drivers (choose based on your database)
# psycopg2-binary>=2.9.0  # PostgreSQL
# pymysql>=1.0.0  # MySQL
# sqlite3  # SQLite (built-in with Python)
